
import { RiArrowLeftCircleFill, RiArrowRightCircleFill } from "react-icons/ri";
import image2 from '../assets/construction2.jpg'

  

  const Carousel = () => {
    



  return (
    <div className=" w-full">
      <div className='relative -z-10'>
        <img className="w-full -z-10 h-120 object-cover" src={image2} alt="image1"/>
        <div className='absolute max-md:left-1/2 top-2/3 -right-1/6 transform -translate-x-1/2 -translate-y-1/2'>
            <h1 className='text-6xl max-lg:text-4xl max-md:pl-7 max-md:p-2 bg-neutral-800 text-amber-400 p-10 rounded-full font-bold'>Build with ingenuity</h1>
            <p className={' text-2xl max-md:text-xl max-md:w-80 max-md:px-5 max-md:py-2 bg-white text-neutral-800 p-5 rounded-full'}>Leading the way in building & construction</p>
            {/* <RiArrowLeftCircleFill onClick={prev} className='absolute top-2/3 -left-2/3 transform -translate-x-1/2 -translate-y-1/2 text-6xl text-white' />
            <RiArrowRightCircleFill onClick={next}  className='absolute top-2/3 -right-1/7 transform -translate-x-1/2 -translate-y-1/2 text-6xl text-white' /> */}
        </div>
      </div>

      {/* <div className='relative'>
      <div className='h-120 object-cover w-full'></div>
        <div className='absolute top-2/3 -right-2/10 transform -translate-x-1/2 -translate-y-1/2'>
            <p className='text-2xl bg-white text-neutral-800 p-5 rounded-full'>Best Modern Technologies</p>
            <h1 className='text-6xl bg-neutral-800 text-amber-400 p-5 rounded-full font-bold'>Construction and Building</h1>
        </div>
        </div> */}
    </div>
  )
}

export default Carousel;