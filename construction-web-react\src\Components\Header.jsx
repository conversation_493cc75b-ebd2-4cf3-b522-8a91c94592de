import { useState } from "react";
import { Link, NavLink } from "react-router-dom";
import logo from "../assets/img_7172.png";


const Header = () => {


    const [isMenuOpen, setIsMenuOpen] = useState(false);

  return (
    <header className='flex h- max-md:text-xl h-10  max-md:hidden max-md:text-center flex-row justify-between items-center text-gray-900 py-18 px-8 md:px-20 bg-white drop-shadow-md'>
        <div className="mr-15 flex flex-row">
            <NavLink to={'/'}>
                <img src={logo} alt="Logo" className="w-30 h-30 "/>
            </NavLink>
            <div className="mt-5 -ml-8">
                <p className=" text-neutral-800 text-2xl"><span className="text-amber-500">H</span>ORIZONENGINE</p>
                <h1 className=" text-neutral-800 text-2xl space-y-10">BUILDERS</h1>
            </div>
        </div>

        <div className="flex flex-row ">
                <div className="flex  flex-row justify-center items-center gap-2 max-md:-ml-2">
                <div className="flex flex-row">
                        <li className="list-none">
                            <NavLink onClick={() => window.location = 'mailto:<EMAIL>'}>
                               <i className="fa-solid fa-phone-volume mx-4 text-lg text-amber-500 border-2 py-5 px-5 rounded-full hover:bg-amber-500 hover:text-neutral-800 hover:border-amber-500 transition-all"></i>
                            </NavLink>
                        </li>
                        <li className="list-none">
                           <p className="text-lg text-neutral-700">Contact Us</p>
                            <p className="text-lg hover:text-amber-500"><strong><EMAIL></strong></p>
                        </li>
                    </div>
                    
                </div>
        </div>
        
        
    </header>
  )
}

export default Header;