import React from 'react';
import { HashRouter, Routes, Route } from "react-router-dom";
import AppLayout from './Component/AppLayout';
import Home from './Component/Pages/Home';
import About from './Component/Pages/About';
import Service from './Component/Pages/Services';
import Contact from './Component/Pages/Contact';
import Project from './Component/Pages/Project';

function App() {

  return (
    <>
      <HashRouter>
      
        <Routes>
          <Route path="/" element={<Home />} />
          <Route path="/about" element={<About />} />
          <Route path="/services" element={<Service />} />
          <Route path="/contact" element={<Contact />} />
          <Route path="/project" element={<Project />} />
        </Routes>
      
    </HashRouter>
    </>
  )
}

export default App;





