import React from 'react';
import { BrowserRouter, Routes, Route } from "react-router-dom";
import Home from './Component/Pages/Home';
import About from './Component/Pages/About';
import Service from './Component/Pages/Services';
import Contact from './Component/Pages/Contact';
import Project from './Component/Pages/Project';

function App() {

  return (
    <>
      <BrowserRouter>
        <Routes>
          <Route path="/" element={<Home />} />
          <Route path="/about" element={<About />} />
          <Route path="/services" element={<Service />} />
          <Route path="/contact" element={<Contact />} />
          <Route path="/project" element={<Project />} />
        </Routes>
      </BrowserRouter>
    </>
  )
}

export default App;





