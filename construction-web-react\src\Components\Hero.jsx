import React from 'react'
import Image8 from '../assets/Image7.jpg'

const Hero = () => {
  return (
    <div className='md:grid grid-cols-3 flex flex-col z-0 md:mt-10 mt-7 max-md:text-center max-md:px-2'>
        <div className='flex flex-col md:col-span-3 md:block'>
            <h2 className='text-5xl font-bold md:block  opacity-14 md:ml-5 text-amber-400'>BUILDING</h2>
            <h3 className='text-3xl p-5 justify-center flex-wrap flex md:block -mt-10 text-neutral-800 font-bold'>Building It Better In <span className='text-amber-400'> Concrete</span></h3>
            <p className='text-lg p-15 md:-ml-9 md:py-12 justify-center flex md:block -mt-16 text-neutral-700'>The Department of Buildings promotes the safety build and construction.</p>
        </div>

        <div className='flex flex-col justify-center -mt-8 items-center'>
            <h2 className='text-8xl font-bold opacity-14 text-amber-400'>01</h2>
            <h3 className='text-3xl p-5 text-center -mt-10 text-neutral-800 font-bold'>Design & Building</h3>
            <p className='text-lg p-15 -mt-16 text-center text-neutral-700'>Services currently available at the Department’s Development HUB have been enhanced.</p>
        </div>
            
        
        <div className='flex flex-col justify-center -mt-8 items-center'>
            <h2 className='text-8xl font-bold opacity-14 text-amber-400'>02</h2>
            <h3 className='text-3xl p-5 text-center -mt-10 text-neutral-800 font-bold'>Safe Construction</h3>
            <p className='text-lg p-15 -mt-16 text-center text-neutral-700'>Applicants and Owners can review plans and objections alongside their plan examiner virtually.</p>
        </div>
    
        <div className='flex flex-col justify-center md:col-start-1 -mt-8 items-center'>
            <h2 className='text-8xl font-bold opacity-14 text-amber-400'>03</h2>
            <h3 className='text-3xl p-5 text-center -mt-10 text-neutral-800 font-bold'>Remodeling</h3>
            <p className='text-lg p-15 -mt-16 text-center text-neutral-700'>The virtual review software eliminates the need for developers to visit a borough office.</p>
        </div>

        <div className='flex flex-col justify-center -mt-8 items-center'>
           <h2 className='text-8xl font-bold opacity-14 text-amber-400'>04</h2>
            <h3 className='text-3xl p-5 text-center -mt-10 text-neutral-800 font-bold'>Tiling Painting</h3>
            <p className='text-lg p-15 -mt-16 text-center text-neutral-700'>Will allow the applicant to access reviewer comments and objections online.</p>
        </div>
        <div className='flex relative flex-row justify-center md:-mt-50 -mt-16 items-center'>
        <img className='z-10' src={Image8} alt='CEO' />
        
        </div>
    </div>
  )
}

export default Hero;