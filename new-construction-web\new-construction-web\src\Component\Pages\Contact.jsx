
import { NavLink } from 'react-router-dom'
import NavBar from '../NavBar'
import Footer from '../Footer'
import ChatBot from '../ChatBot/ChatBot'

function Contact() {
  return (
    <>
    <NavBar />
    <div className='contact-form mt-10'>
      <div className=''>
      <h1 className='text-6xl max-md:text-center max-md:text-4xl font-bold mb-15 text-amber-400 opacity-30'>Send us a message</h1>
      <form className='flex flex-col gap-10'>
        <label className='-mb-6'>Name</label>
        <input type="text " className='outline border-1 py-4 px-20 placeholder:text-neutral-200 input-class ' placeholder='Name' required />
        <label className='-mb-6'>Email</label>
        <input type="email" className='outline border-1 py-4 px-20 placeholder:text-neutral-200 input-class ' placeholder='Email' required />
        <label className='-mb-6'>Message</label>
        <textarea rows={10} type="text" className='outline border-1 py-4 px-20 placeholder:text-neutral-200 input-class ' placeholder='Message' required />
        <button className='border-1 bg-amber-400 px-20 py-2 btn'>Submit</button>
      </form>
    </div>
    </div>
    <ChatBot />
    <Footer />
    </>
  )
}

export default Contact