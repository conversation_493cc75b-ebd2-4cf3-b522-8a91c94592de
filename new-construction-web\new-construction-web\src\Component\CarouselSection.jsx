import tractor1 from "../assets/tractor1.jpg"
import tractor2 from "../assets/tractor2.jpg"
import tractor3 from "../assets/tractor3.jpg"
import tractor4 from "../assets/tractor4.jpg"

function CarouselSection() {
  return (
    <div>
        <div className='about-container margin-style'>
            <h2 className='text-amber-300 text-center text-4xl opacity-30 about-heading'>Heavy Machinery Sales – Horizoneng Builders</h2>
            <div className='about-wrapper'>
            <p className='about-content'>At Horizoneng Builders, we don’t just build—we equip others to build too. As part of our commitment to supporting the construction industry, we offer the sale of high-quality heavy construction machinery suitable for all types of projects.

              We understand the importance of durable, efficient, and reliable equipment on the job site. That’s why we provide machines that meet industry standards and are built to handle even the toughest tasks. Whether you’re starting a new project or upgrading your equipment, Horizoneng Builders is your trusted source for heavy-duty machines.</p>
            </div>
        
            <h3 className='text-amber-300 text-center text-2xl opacity-30 about-heading'>Our Heavy Equipment for Sale Includes:</h3>
            <div className='about-wrapper'>
            <ol className='about-content list-disc'>
                <li> &#9688; Excavators – Ideal for digging, trenching, and material handling.</li>
                <li> &#9688; Bulldozers – Powerful machines for earthmoving and land clearing.</li>
                <li> &#9688; Backhoe Loaders – Versatile equipment for digging and backfilling.</li>
                <li>  &#9688; Wheel Loaders – Perfect for loading, transporting, and leveling materials.</li>
                <li>  &#9688; Cranes – Designed for heavy lifting and vertical construction needs.</li>
                <li>  &#9688; Motor Graders – Used for road construction, leveling, and fine grading and so many more.</li>
            </ol>
            </div>
            <div className="flex gap-4 justify-evenly max-md:flex-col flex-row w-45 carousel-image">
            <img className="" src={tractor1} alt="Tractor" />
            <img className="" src={tractor2} alt="Tractor" />
            <img className="" src={tractor3} alt="Tractor" />
            <img className="" src={tractor4} alt="Tractor" />
            </div>
        </div>
    </div>
  )
}

export default CarouselSection