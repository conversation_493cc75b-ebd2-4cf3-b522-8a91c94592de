@import "tailwindcss";
@import url('https://fonts.googleapis.com/css2?family=Jost:ital,wght@0,100..900;1,100..900&family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap');



* {
  padding: 0;
  margin: 0;
  box-sizing: border-box;
}

body {
  font-family: "Poppins", sans-serif;
  position: relative;
  
}

body::before {
  content: "";
  position: absolute;
  height: 100%;
  width: 100%;
  
  z-index: -1;
}

a {
  text-decoration: none;
}

header {
  background: #f1c720;
  box-shadow: 0 0 5px rgba(0, 0, 0, 0.2);
  padding: 2rem 0;
  position: relative;
}



.container {
  max-width: 1440px;
  width: 100%;
  margin: 0 auto;
  padding: 0 1rem;
  
}

.container nav {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.container nav .logo {

 cursor: pointer;
}

nav .logo img {
  width: 400px;
  object-fit: cover;
}



.container .nav-links {
  display: flex;
}

.container .nav-links li {
  list-style: none;
  margin: 0 0.5rem;
}

.container ul li a {
  color: #212a3c;
  font-size: 1.3rem;
  padding: 0.7rem 1rem;
  display: block;
  border-radius: 10px;
}


.container ul li a:hover {
  background: #f1c720;
  color: #fff;
}

.container ul li a:hover, .container ul li a.active {
  background: #212a3c;
  color: #fff;
}

.content {
  position: absolute;
  top: 45%;
  left: 50%;
  transform: translate(-50%, 0%);
}

.content h2 {
  color: #fff;
  font-size: 3rem;
}

.content p {
  color: #212a3c;
  font-size: 1.5rem;
  padding: 10px;
  background-color: #f1c720;
  border-radius: 50px;
}

.icon {
  font-size: 1.5rem;
  cursor: pointer;
  display: none;
}

.logo img {
  width: auto;
  height: 100px;
}




.about-container .about-heading {
  width: 85%;
  margin: auto;
  text-align: center;
}

.about-container .about-heading h1 {
  font-size: 2.5rem;
  margin-top: 40px;
  color: #f1c720;
  opacity: 0.5;
}

.about-wrapper {
  width: 85%;
  margin: 50px auto;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.about-content {
  width: 50%;
  margin: 0px 25px;
  transition: all 0.7s;
}

.about-content-review {
  width: 100%;
  margin: 0px 25px;
  transition: all 0.7s;
}

.margin-style {
  margin-top: 40px;
}

.about3-content {
  width: 25%;
  margin: 0px 25px;
}

.about-content:hover {
  transform: translateY(-10px);
}

.about-content h2 {
  font-size: 30px;
  color: #212a3c;
}

.about-content p {
  margin-top: 10px;
  font-size: 20px;
  color: #212a3c;
  line-height: 1.;
}

.about-img {
  width: 50%;
  transition: all 0.7s;

}

.about-img:hover {
  transform: translateY(-10px);
}

.about-img img {
  width: 100%;
  height: auto;
}



.carousel-containe {
  height: fit-content;
  background-image: url("./assets/construction2.jpg");
  background-position: center;
  background-repeat: no-repeat;
  background-size: cover;
  height: 100vh;
  width: 100%;
  margin: auto;
  position: relative;
  padding: 20px 0px;
  

}

.carousel-content img {
  width: 80%;
  
}

.carousel-image {
  margin-left: 40px;
 
}



.carousel-content h2  {
  position: absolute;
  bottom: 30%;
  right: 10%;
  border-radius: 30px;
  margin-bottom: 10px;
  background-color: #f1c720;
  color: #212a3c;
  font-size: 2rem;
  padding: 10px;
  transition: all 0.4s ease-in-out;
}

.carousel-content h2:hover,
.carousel-content p:hover {
  transform: translateX(10px);
}

.carousel-content h2 {
  position: absolute;
      bottom: 25%;
      left: 30%;
      background-color: #f1c720;
      border-radius: 50px;
      color: #212a3c;
      font-size: 2rem;
      padding: 10px 20px;
      transition: all 0.4s ease-in-out;
}

.carousel-content p {
  position: absolute;
      bottom: 14%;
      left: 25%;
      background-color: #f1c720;
      border-radius: 50px;
      color: #212a3c;
      font-size: 2rem;
      padding: 10px 25px;
      transition: all 0.4s ease-in-out;
}

.contact-form {
  width: 85%;
  margin: auto;
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  margin-top: 20px;
}

.input-class {
  padding: 10px;
  margin-top: -25px;
}

.btn {
  padding: 10px 15px;
  border-radius: 25px;
  font-size: 1rem;
}

.section-padding {
  padding: 4rem 4rem;
}

footer {
  background-color: #2d2d32;
}

.sb-footer {
  display: flex;
  flex-direction: column;
  background-color: #2d2d32;
}

.sb-footer-links {
  display: flex;
  justify-content: space-between;
  width: 100%;
  align-items: flex-start;
  flex-direction: row;
  flex-wrap: wrap;
  text-align: center;
  margin-bottom: 2rem;
}

a {
  color: rgb(175, 175, 179);
  text-decoration: none;
}

.sb-footer-links-div {
  width: 150px;
  margin : 1rem;
  display: flex;
  justify-content: flex-start;
  flex-direction: column;
  color: #fff;
}

.sb-footer-links-div h4 {
  font-size: 14px;
  line-height: 17px;
  margin-bottom: 0.9rem;
}

.sb-footer-links-div a {
  font-size: 12px;
  line-height: 15px;
  margin-bottom: 0.9rem;
}

.footer-below {
  display: flex;
  justify-content: space-between;
  flex-direction: row;
  margin-top: 0.2rem;
}

.footer-copyright p {
  font-size: 13px;
  line-height: 15px;
  font-weight: 600;
  color: rgb(255, 255, 255);

}


@media  (max-width: 768px) {
  .content h2 {
    font-size: 4rem;
    text-align: center;
  }

  .icon {
    display: block;
  }

  .carousel-image {
    margin: auto;
  }

  .container .nav-links {
    flex-direction: column;
    justify-content: space-evenly;
    background-color: #f1c720;
    position: absolute;
    width: 350px;
    height: 60vh;
    top: -800px;
    right: 0;
    z-index: 10;
    transition: all 0.7s;
  }

  .nav-links.active {
      top: 98%;
  }

  .about-wrapper {
    flex-direction: column;
    margin-top: 0px;
  }

  .about-container .about-heading h1 {
    font-size: 40px;
  }

  .about-content, .about-img {
    width: 100%;
    margin-top: 20px;
  }

  .carousel-image {
    width: 80%;
  }

  .about-content h2 {
    font-size: 25px;
  }

  
}



#chatbot-toggle {
  width: 50px;
  height: 50px;
  border-radius: 50px;
  background-color: rgb(255, 197, 51);
  color: #000;
  box-shadow: 0 0 128px 0 rgba(0, 0, 0, 0.1), 0 32px 64px -48px rgba(0, 0, 0, 0.5);
  z-index: 999;
  transition: all 0.3s ease 0s;
}

#chatbot-toggle:hover,
#chatbot-toggle span:hover {
  background-color: #fff;
}



#chatbot-toggle span {
  position: absolute;
  color: #333;
  top: 8px;
  left: 8px;
}

.container.show-chatbot #chatbot-toggle {
  transform: rotate(90deg);
}

#chatbot-toggle span:last-child,
.container.show-chatbot #chatbot-toggle span:first-child {
  opacity: 0;
}

#chatbot-toggle span:first-child,
.container.show-chatbot #chatbot-toggle span:last-child {
  opacity: 1;
}



/* Chat Bot */

.chatbot-popup {
  position: fixe;
  opacity: 0;
  pointer-events: none;
  transform: scale(0.2);
  transform-origin: bottom right;
  width: 380p;
  background-color: #fff;
  color: #000;
  overflow: hidden;
  border-radius: 15px;
  box-shadow: 0 0 128px 0 rgba(0, 0, 0, 0.1), 0 32px 64px -48px rgba(0, 0, 0, 0.5);
  z-index: 999;
  cursor: pointer;
  z-index: 999;
  transition: all 0.3s ease 0s;
}

.container.show-chatbot > .chatbot-popup {
  opacity: 1;
  pointer-events: auto;
  transform: scale(1);
}

.chatbot-popup > .chatbot-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 22px;
}

.chatbot-header > .header-info {
  display: flex;
  align-items: center;
  gap: 10px;
}

.header-info > .logo-text {
  color: #fff;
  font-size: 1.3rem;
  font-weight: 600;

}

.chatbot-body {
  display: flex;
  height: 460px;
  overflow-y: auto;
  padding: 25px 22px;
  margin-left: 5px;
  flex-direction: column;
  gap: 20px;
  padding-bottom: 90px;
  scrollbar-width: thin;
  scrollbar-color: rgb(255, 242, 208) transparent;
}

.chatbot-body > .message {
  display: flex;
  gap: 11px;
  align-items: center;
}

.chatbot-body > .message.error .message-text {
  color: #ff0000;
}

.message > .message-text {
  background-color: rgb(255, 242, 208);
  border-radius: 13px 13px 13px 3px;
  padding: 12px 16px;
  max-width: 75%;
  word-wrap: break-word;
  white-space: pre-line;
  font-size: 0.95rem;
}

.chatbot-body > .chatbot-message > .message-text {
  background-color: rgb(255, 242, 208);
  border-radius: 13px 13px 13px 3px;
}

.user-message {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

.user-message > .message-text {
  background-color: rgb(255, 197, 51);
  border-radius: 13px 13px 3px 13px;
  color: #fff;
  max-width: 75%;
  padding: 18px 14px;
  word-wrap: break-word;
}  

.chat-footer {
  position: absolute;
  top: 30px;
  width: 100%;
  padding: 15px 22px;
  background-color: #fff;
  padding: 15px 22px 20px;
  margin-top: 50px;
}

.chat-footer > .chat-form {
  display: flex;
  align-items: center;
  background-color: #fff;
  box-shadow: 0 0 8px rgb(255, 197, 51);
  border-radius: 32px;
  border: 1px solid #cccce5;
}

.chat-form > .message-input {
  border-radius: 20px;
  border: none;
  outline: none;
  background: #fff;
  height: 47px;
  padding: 0 18px;
  margin-top: 18px;
  margin-bottom: -10px;
  margin-left: 20px;
  width: 100%;
  font-size: 0.95rem;
}



.chat-form:valid~button {
  display: block;
}



@media screen and (max-width: 850px) {
  .footer-header h1 {
    font-size: 44px;
    line-height: 50px;
  }
  
  .carousel-image {
    width: 80%;
    margin: auto;
    flex-direction: column;
  }

}

@media screen and (max-width: 550px) {
  .footer-header h1 {
    font-size: 34px;
    line-height: 42px;
  }

  .footer-links div {
        margin: 3rem 0;
      }

      .footer-below {
        flex-direction: column-reverse;
        justify-content: left;
      }

  
}

.carousel-containe img {
  width: 80%;
  height: auto;
  margin: auto;
  background-color: aquamarine;
}



@media screen and (max-width: 400px) {
  .footer-header h1 {
    font-size: 24px;
    line-height: 32px;
  }
  
}

.about-wrapper li {
  list-style: none;
}