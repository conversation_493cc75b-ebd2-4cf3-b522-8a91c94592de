import React, { useState } from "react";
import { Link, NavLink } from "react-router-dom";
import { motion } from "framer-motion";
import { RiMenu3Line, RiPhoneFill } from "react-icons/ri"; 


const NavBar = () => {

  const [toggle, setToggle] = useState(false);

  return (
    <>
    <div className=" header w-full rounded-full bg-white md:bg-neutral-800 drop-shadow-md text-white p-5 max-md:flex justify-between">
    <Link to={'javascript:void(0)'} onClick={() => window.location = 'mailto:<EMAIL>'}>
       <RiPhoneFill className="rounded-full bg-amber-400 text-neutral-800 text-xl w-10 h-10 p-2 md:hidden" />
    </Link>
      
        <div className="middle-div px-5 md:hidden">
            <p className="font-bold text-neutral-800 text-xl"><span className="text-amber-400">H</span>ORIZONENG</p>
            <h1 className=" text-neutral-800 text-xs space-y-10">BUILDERS</h1>
        </div>
      <div className="right-div px-2 ">
             <ul className='justify-around items-center max-md:gap-8 font-jost md:flex hidden'>
                <Link className='px-7 py-2 hover:bg-amber-400 hover:text-neutral-800 rounded-full' to={"/"}>Home</Link>
                <Link className='px-7 py-2 hover:bg-amber-400 hover:text-neutral-800 rounded-full' to={"/about"}>The Company</Link>
                <Link className='px-7 py-2 hover:bg-amber-400 hover:text-neutral-800 rounded-full' to={"/services"}>Services</Link>
                <Link className='px-7 py-2 hover:bg-amber-400 hover:text-neutral-800 rounded-full' to={"/project"}>Project</Link>
                <Link className='px-7 py-2 hover:bg-amber-400 hover:text-neutral-800 rounded-full' to={"/contact"}>Contact</Link>
            </ul>
            <RiMenu3Line className='text-xl md:hidden block rounded-full bg-amber-400 text-neutral-800 w-10 h-10 p-2' onClick={() => setToggle(!toggle)} />
      </div>
      {/* {Sidebar} */}
      
      <div className={`sidebar-main-container transition-all w-full z-50 h-screen bg-none fixed  top-0  flex justify-end 
      ${toggle ? "right-0 duration-500 ease-in-out" : "left-[-100%] duration-500 ease-in"}`}
       onClick={() => setToggle(!toggle)}>
        <div className="child h-full w-70 bg-amber-400 flex justify-center items-center">
          <ul className="font-jost flex flex-col gap-4">
            <motion.div className="mb-3" 
            initial={{opacity:0, x:100}} 
            whileInView={{opacity:1, x:0}}
            transition={{duration:0.7, ease:"easeIn", stiffness:90}}>
                <NavLink className='text-xl py-5 px-7 font-jost rounded-full'
                onClick={() => setToggle(!toggle)} to={"/"}>Home</NavLink>
            </motion.div>

            <motion.div className="mb-3"
            initial={{opacity:0, x:100}} 
            whileInView={{opacity:1, x:0}}
            transition={{duration:0.7, ease:"easeIn", stiffness:90}}>
            <NavLink className='text-xl py-5 px-7 font-jost rounded-full' 
          onClick={() => setToggle(!toggle)} to={"/about"}>The Company</NavLink>
            </motion.div>

            <motion.div className="mb-3"
            initial={{opacity:0, x:100}} 
            whileInView={{opacity:1, x:0}}
            transition={{duration:0.7, ease:"easeIn", stiffness:90}}>
            <NavLink className='text-xl py-5 px-7 font-jost rounded-full' 
          onClick={() => setToggle(!toggle)} to={"/services"}>Services</NavLink>
            </motion.div>

            

            <motion.div className="mb-3"
            initial={{opacity:0, x:100}} 
            whileInView={{opacity:1, x:0}}
            transition={{duration:0.7, ease:"easeIn", stiffness:90}}>
            <NavLink className='font-jost text-xl py-5 px-7 rounded-full' 
          onClick={() => setToggle(!toggle)} to={"/project"}>Projects</NavLink>
            </motion.div>

            <motion.div className="mb-3"
            initial={{opacity:0, x:100}} 
            whileInView={{opacity:1, x:0}}
            transition={{duration:0.7, ease:"easeIn", stiffness:90}}>
            <NavLink className='text-xl py-5 px-7 rounded-full' 
          onClick={() => setToggle(!toggle)} to={"/contact"}>Contact</NavLink>
            </motion.div>
          </ul>
        </div>
      </div>
    </div>
    </>
  )
}

export default NavBar;