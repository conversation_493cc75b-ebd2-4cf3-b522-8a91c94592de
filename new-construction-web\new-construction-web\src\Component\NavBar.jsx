
import { FaBars } from 'react-icons/fa6'
import React, { useState } from 'react'
import { Link, NavLink } from 'react-router-dom';
import logo from '../assets/logo.png'

function NavBar() {

    const [isOpen, setIsOpen] = useState(false);

    const toggleMenu = () => {
      setIsOpen(!isOpen);
    };


  return (
    <>
    <header>
        <div className='container'>
        
        <nav>
        <div className='logo'>
          <h1 className='text-4xl'>HORIZONENG</h1>
          <p className='text-2xl'>BUILDERS</p>
        </div>
        <ul className={isOpen ? 'nav-links active' : 'nav-links'}>
                <li><Link className='active' to={"/"}>Home</Link></li>
                <li><Link to={"/about"}>About</Link></li>
                <li><Link to={"/services"}>Services</Link></li>
                <li><Link to={"/project"}>Projects</Link></li>
                <li><Link to={"/contact"}>Contact</Link></li>
            </ul>
        <div className="icon" onClick={toggleMenu}><FaBars /></div>
      </nav>
    </div>
    </header>
     <section >
        <div className='container'>
        
        </div>
    </section> 
    </>
  )
}

export default NavBar;