@import "tailwindcss";



* {
    scroll-behavior: smooth;
    box-sizing: border-box;

}

.sidebar-main-container ul a:hover {
    background-color: #333;
    color: #fff;
}

.right-div ul a:active {
    background-color: #333;
}

.right-div ul a:focus {
    background-color: rgb(255, 197, 51);
}

#chatbot-toggle {
    width: 50px;
    height: 50px;
    border-radius: 50px;
    background-color: rgb(255, 197, 51);
    color: #000;
    box-shadow: 0 0 128px 0 rgba(0, 0, 0, 0.1), 0 32px 64px -48px rgba(0, 0, 0, 0.5);
    z-index: 999;
    transition: all 0.3s ease 0s;
}

#chatbot-toggle:hover,
#chatbot-toggle span:hover {
    background-color: #fff;
}



#chatbot-toggle span {
    position: absolute;
    color: #333;
    top: 8px;
    left: 8px;
}

.container.show-chatbot #chatbot-toggle {
    transform: rotate(90deg);
}

#chatbot-toggle span:last-child,
.container.show-chatbot #chatbot-toggle span:first-child {
    opacity: 0;
}

#chatbot-toggle span:first-child,
.container.show-chatbot #chatbot-toggle span:last-child {
    opacity: 1;
}



/* Chat Bot */

.chatbot-popup {
    position: fixe;
    opacity: 0;
    pointer-events: none;
    transform: scale(0.2);
    transform-origin: bottom right;
    width: 380p;
    background-color: #fff;
    color: #000;
    overflow: hidden;
    border-radius: 15px;
    box-shadow: 0 0 128px 0 rgba(0, 0, 0, 0.1), 0 32px 64px -48px rgba(0, 0, 0, 0.5);
    z-index: 999;
    cursor: pointer;
    z-index: 999;
    transition: all 0.3s ease 0s;
}

.container.show-chatbot > .chatbot-popup {
    opacity: 1;
    pointer-events: auto;
    transform: scale(1);
}

.chatbot-popup > .chatbot-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 22px;
}

.chatbot-header > .header-info {
    display: flex;
    align-items: center;
    gap: 10px;
}

.header-info > .logo-text {
    color: #fff;
    font-size: 1.3rem;
    font-weight: 600;

}

.chatbot-body {
    display: flex;
    height: 460px;
    overflow-y: auto;
    padding: 25px 22px;
    flex-direction: column;
    gap: 20px;
    padding-bottom: 90px;
    scrollbar-width: thin;
    scrollbar-color: rgb(255, 242, 208) transparent;
}

.chatbot-body > .message {
    display: flex;
    gap: 11px;
    align-items: center;
}

.chatbot-body > .message.error .message-text {
    color: #ff0000;
}

.message > .message-text {
    background-color: rgb(255, 242, 208);
    border-radius: 13px 13px 13px 3px;
    padding: 12px 16px;
    max-width: 75%;
    word-wrap: break-word;
    white-space: pre-line;
    font-size: 0.95rem;
}

.chatbot-body > .chatbot-message > .message-text {
    background-color: rgb(255, 242, 208);
    border-radius: 13px 13px 13px 3px;
}

.user-message {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
}

.user-message > .message-text {
    background-color: rgb(255, 197, 51);
    border-radius: 13px 13px 3px 13px;
    color: #fff;
    max-width: 75%;
    padding: 18px 14px;
    word-wrap: break-word;
}  

.chat-footer {
    position: absolute;
    top: 20px;
    width: 100%;
    padding: 15px 22px;
    background-color: #fff;
    padding: 15px 22px 20px;
    margin-top: 50px;
}

.chat-footer > .chat-form {
    display: flex;
    align-items: center;
    background-color: #fff;
    box-shadow: 0 0 8px rgb(255, 197, 51);
    border-radius: 32px;
    border: 1px solid #cccce5;
}

.chat-form > .message-input {
    border-radius: 20px;
    border: none;
    background: #fff;
    height: 47px;
    padding: 0 20px;
    width: 100%;
    font-size: 0.95rem;
}



.chat-form:valid~button {
    display: block;
}


.slick-slide>div {
    margin: 0 10px;
}


main {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 500px;
    background: rgb(255, 197, 51);
    overflow: hidden;
    margin-top: 30px;
}

.container {
    width: 90%;
}

.swiper {
    
    width: 100%;
    padding: 60px 10px;
}

.swiper-slide {
    position: relative;
    width: 300px;
    height: 300px;
    background: #fff;
    box-shadow: 1px 22px 44px rgba(0, 0, 0, 0.2);
    border-radius: 25px;
    overflow: hidden;
    transition: 1s;
    user-select: none;
}

.swiper-slide-active {
    border: 3px solid #fff;
    transition: 0.6s ease-in-out;
}

.slide-1 {
    background: url("../public/building1.jpg");
    background-repeat: no-repeat;
    background-size: cover;
    background-position: center;
}

.slide-2 {
    background: url("../public/project1.jpg");
    background-repeat: no-repeat;
    background-size: cover;
    background-position: 35%;
}

.slide-3 {
    background: url("../public/project2.jpg");
    background-repeat: no-repeat;
    background-size: cover;
    background-position: 45%;
}

.slide-4 {
    background: url("../public/building4.jpg");
    background-repeat: no-repeat;
    background-size: cover;
    background-position: center;
}

.slide-5 {
    background: url("../public/building5.jpg");
    background-repeat: no-repeat;
    background-size: cover;
    background-position: center;
}

.slide-6 {
    background: url("../public/project4.jpg");
    background-repeat: no-repeat;
    background-size: cover;
    background-position: center;
}

.slide-7 {
    background: url("../public/project3.jpg");
    background-repeat: no-repeat;
    background-size: cover;
    background-position: center;
}


.title {
    display: flex;
    flex-direction: column;
    justify-content: center;
    height: 100%;
    padding: 25px 25px 38px;
    
}

.title h1 {
    font-size: 2rem;
    font-weight: 600;
    color: #fff;
    border-radius: 50px;
    padding: 10px 15px;
    opacity: 1;
}

.swiper-slide-active .title h1 {
    opacity: 0;
    transition: 0.8s;
    transition-delay: 1.5s;
}

.content {
    position: absolute;
    inset: 0;
    width: 100%;
    height: 100%;
    padding: 25px 25px 65px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    background: rgba(0, 0, 0, 0.5);
    background-image: linear-gradient(180deg, transparent, transparent, rgba(0, 0, 0, 0.6), rgba(0, 0, 0, 0.8));
    color: #fff;
    opacity: 0;
}

.swiper-slide-active .content {
    opacity: 1;
    transition: 0.8s;
    transition-delay: 1.5s;
}

.score {
    position: absolute;
    top: 10px;
    right: 10px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    aspect-ratio: 1/1;
    width: 45px;
    height: 50px;
    border-radius: 50px;
    background-color: #ffc75a;
    color: #000;
    box-shadow: 0 0 128px 0 rgba(0, 0, 0, 0.1), 0 32px 64px -48px rgba(0, 0, 0, 0.5),inset 0 -2px 6px 0 r ;
    opacity: 0;
    font-size: 1rem;
    font-weight: 600;
}

.swiper-slide-active .score {
    opacity: 1;
    transition: 0.8s;
    transition-delay: 0.9s;
}

.text {
    border-left: 4px solid #ffc75a;
    padding-left: 10px;
    opacity: 0;
}

.text h2 {
    font-size: 1.5rem;
    font-weight: 700;
    font-weight: 600;
    margin-bottom: 12px;
    transform: translateY(-100%);
    opacity: 0;
}

.swiper-slide-active .text h2 {
    transform: translateY(0);
    opacity: 1;
    transition: 1s;
    transition-delay: 2.2s;
}

.text p {
    font-size: 0.9rem;
    font-weight: 400;
    line-height: 1.4;
    transform: translateY(100%);
    opacity: 0;
}

.swiper-slide-active .text p {
    transform: translateY(0);
    opacity: 1;
    transition: 1.5s;
    transition-delay: 1.8s;
}

.genre {
    position: absolute;
    bottom: 20px;
    left: 25px;
    display: flex;
    gap: 10;
}

.swiper-pagination-bullet {
    width: 16px;
    height: 16px;
    background-color: #fff;
    border-radius: 50%;
}

.swiper-pagination-bullet-active {
    background-color: #fff;
    border-radius: 14px;
    background: linear-gradient(120deg, #fed11b, #ffc75a);
    box-shadow: 0 0 20px 0 rgba(255, 200, 80, 0.7);
}

@media screen and (max-width: 768px) {
    .swiper-slide {
        width: 300px;
    }

    .title h1 {
        font-size: 2rem;
    }

    .score {
        width: 40px;
        font-size: 0.8rem;
    }

    .text h2 {
        font-size: 1.2rem;
        margin-bottom: 8px;
    }

    .text p {
        font-size: 0.8rem;
        line-height: 1.3;
    }

    .swiper-pagination-bullet {
        width: 12px;
        height: 12px;
    }

    .swiper-pagination-bullet-active {
        width: 28px;
        border-radius: 14px;
    }
    
    
   

}