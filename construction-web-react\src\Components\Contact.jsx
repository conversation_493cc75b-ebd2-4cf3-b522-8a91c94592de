import React from 'react'

const contact = () => {
  return (
    <div className='flex cursor-pointer mt-20 mb-20 flex-col items-center z-0'>
      <h1 className='text-6xl max-md:text-center max-md:text-4xl font-bold mb-15 text-amber-400 opacity-30'>Send us a message</h1>
      <form className='flex flex-col gap-10'>
        <label className='-mb-10'>Name</label>
        <input type="text " className='outline border-1 py-4 px-20 placeholder:text-neutral-50 ' placeholder='Name' required />
        <label className='-mb-10'>Email</label>
        <input type="email" className='outline border-1 py-4 px-20 placeholder:text-neutral-50 ' placeholder='Name' required />
        <label className='-mb-10'>Message</label>
        <input type="text" className='outline border-1 py-4 px-20 placeholder:text-neutral-50 ' placeholder='Name' required />
        <button className='border-1 bg-amber-400 px-20 py-2'>Submit</button>
      </form>
    </div>
  )
}

export default contact