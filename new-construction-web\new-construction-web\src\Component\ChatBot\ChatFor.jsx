
import { useRef } from 'react';
import { RiArrowUpLine } from 'react-icons/ri'

const ChatFor = ({ chatHistory, setChatHistory, generateBotResponse }) => {
    

    const inputRef = useRef();

    const handleFormSubmit = (e) => {
        e.preventDefault();
        const userMessage = inputRef.current.value.trim();
        if (!userMessage) return;
        inputRef.current.value = "";

        setChatHistory((history) => [...history, { role: "user", text: userMessage }]);

        setTimeout(() => {setChatHistory((history) => [...history, { role: "model", text: "Thinking..." }]);

        generateBotResponse([...chatHistory, { role: "user", text: `Usings the details provided above, please address this query: ${userMessage}` }]);

    }, 600);

    };



  return (
    <div>
        <form action="#" className='chat-form ml-10' onSubmit={handleFormSubmit}>
            <input ref={inputRef} type="text" className='message-input border-none bloc shadow-amber-400 border-neutral-200' placeholder='Type a message...' required />
            <button className='absolute right-4 top-7'>
                <RiArrowUpLine className='h-7 w-7  shadow-md rounded-full bg-amber-400 cursor-pointer hover:bg-amber-500 transition-all ease-in-out text-white' />
            </button>
        </form>
    </div>
  )
}

export default ChatFor;