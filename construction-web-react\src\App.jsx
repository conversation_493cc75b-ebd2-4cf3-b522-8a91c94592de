import React from 'react';
import { HashRouter, Routes, Route } from "react-router-dom";
import AppLayout from './Components/AppLayout';
import Home from './Components/Home';
import About from './Components/About';
import Contact from './Components/Contact';
import Service from './Components/Service';
import Project from './Components/Project';

const App = () => {
  return (
    <HashRouter>
      <AppLayout>
        <Routes>
          <Route path="/" element={<Home />} />
          <Route path="/about" element={<About />} />
          <Route path="/services" element={<Service />} />
          <Route path="/contact" element={<Contact />} />
          <Route path="/project" element={<Project />} />
        </Routes>
      </AppLayout>
    </HashRouter>
  );
};
