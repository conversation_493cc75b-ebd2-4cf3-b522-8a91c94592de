import { RiChatSmileAiLine } from "react-icons/ri";

const ChatMessage = ({ chat }) => {
    return (
        !chat.hideInChat && (
        <div className=''>
            <div className={`message ${chat.role === "model" ? 'bot' : "user"}-message ${chat.isError ? "error" : ""}`}>
             {chat.role === "model" && <RiChatSmileAiLine className='h-5 w-5 self-end bg-amber-500 text-white rounded-full' />}  
                 <p className='message-text '>{chat.text}</p>
            </div>
        </div>
    )
   );
  };


  export default ChatMessage;

