import React from 'react'
import { RiCheckFill } from 'react-icons/ri';
import image from '../assets/image8.jpg'

const Service = () => {
  return (
    <div className='z-10'>
        <div className='flex flex-col justify-center md:gap-5 items-center md:grid -z-50 grid-cols-2'>
    <div className='flex flex-col z-0 mt-7 px-0'>
        <h2 className='text-5xl font-bold md:block md:text-7xl opacity-14 text-amber-400 flex justify-center'>Our Services</h2>
        <h3 className='text-3xl p-5 justify-center md:block md:px-2 md:-mt-10 flex -mt-5 text-neutral-800 font-bold'>What <span className='text-amber-400'>&nbsp; We Do?</span></h3>
        <p className='text-lg p-10 justify-center md:block md:text-l md:px-2 md:-mt-14 w-full max-md:text-center flex -mt-10 px-20 text-neutral-700'>For inquries, feel free to reach <NAME_EMAIL>

Our website, At Horizon Eng Builder, we are dedicated to shaping the future through innovative engineering and construction solutions. Founded on the principles of integrity, precision, and performance, we bring together a team of experienced professionals who are passionate about building structures that last and relationships that matter.

With a strong focus on quality, safety, and sustainability, we specialize in delivering high-impact civil, structural, and infrastructure projects across a range of sectors. Whether it's a commercial build, industrial facility, or public works project, our commitment remains the same: to exceed client expectations and contribute positively to the communities we serve.

As we look to the horizon, we continue to evolve with technology and industry standards—never losing sight of the trust our clients place in us.

Horizon Eng Builder — Building Beyond Boundaries.</p>
<ul className='flex ml-10 text-center gap- md:grid list-none text-nowrap justify-center flex-col md:text-xs md:-ml-10 md:grid-cols-2 mr-14'>
        <li className='flex flex-row md:px-2 '>
            <div><RiCheckFill className='text-amber-400 p-2 hover:bg-amber-400 hover:text-neutral-800  ml-10 bg-amber-50 w-13 h-13 rounded-full' /></div>
            <div className='font-semibold ml-3 p-2 text-lg md:text-lg md:font-light'>Structural Engineering & Design</div>
        </li>
        <li className='flex flex-row'>
            <div><RiCheckFill className='text-amber-400 p-2 hover:bg-amber-400 hover:text-neutral-800  ml-10 bg-amber-50 w-13 h-13 rounded-full' /></div>
            <div className='font-semibold ml-3 p-2 text-lg md:text-lg md:font-light'>Project Management</div>
        </li>
        <li className='flex flex-row'>
            <div><RiCheckFill className='text-amber-400 p-2 hover:bg-amber-400 hover:text-neutral-800 ml-10 bg-amber-50 w-13 h-13 rounded-full' /></div>
            <div className='font-semibold ml-3 p-2 text-lg md:text-lg md:font-light'>Honest And Dependable</div>
        </li>
        <li className='flex flex-row'>
            <div><RiCheckFill className='text-amber-400 p-2 hover:bg-amber-400 hover:text-neutral-800 ml-10 bg-amber-50 w-13 h-13 rounded-full' /></div>
            <div className='font-semibold ml-3 p-2 text-lg md:text-lg md:font-light'>We Are Always Improving</div>
        </li>
        <li className='flex flex-row'>
            <div><RiCheckFill className='text-amber-400 p-2 hover:bg-amber-400 hover:text-neutral-800 ml-10 bg-amber-50 w-13 h-13 rounded-full' /></div>
            <div className='font-semibold ml-3 p-2 text-lg md:text-lg md:font-light'>Commercial Construction</div>
        </li>
        <li className='flex flex-row'>
            <div><RiCheckFill className='text-amber-400 p-2 hover:bg-amber-400 hover:text-neutral-800 ml-10 bg-amber-50 w-13 h-13 rounded-full' /></div>
            <div className='font-semibold ml-3 p-2 text-lg md:text-lg md:font-light'>Residential Construction</div>
        </li>
    </ul>
    </div>
    <div className='md:ml-15 mt-10 '>
        <img src={image} alt='CEO' className='rounded-2xl'/>
    </div>
    </div>
    </div>
  )
}

export default Service;