import { Navigation, Pagination, Scrollbar, A11y } from 'swiper/modules';

import { Swiper, SwiperSlide } from 'swiper/react';

// Import Swiper styles
import 'swiper/css';
import 'swiper/css/navigation';
import 'swiper/css/pagination';
import 'swiper/css/scrollbar';


const Reviews = () => {

  return (
        <main>
            <div className='container'>
            <Swiper
      // install Swiper modules
                modules={[Navigation, Pagination]}
                grabCursor={true}
                initialSlide={2}
                centeredSlides={true}
                slidesPerView="auto"
                speed={1000}
                slideToClickedSlide={true}
                pagination={{ el: ".swiper-pagination", clickable: true }}
                breakpoints={
                    {
                    320: {
                        spaceBetween: 40,
                    },
                    430: {
                        spaceBetween: 50,
                    },
                    580: {
                        spaceBetween: 70,
                    },
                    650: {
                        spaceBetween: 30,
                    }
                }}
                >
                <SwiperSlide className='swiper-slide slide-1'>
                    <div className='title'>
                        <h1>Green House</h1>
                    </div>
                    <div className='content'>
                        <div className='score'>8.5</div>
                        <div className='text'>
                            <h2>Green House</h2>
                            <p>Overview Renovation</p>
                        </div>
                    </div>
                </SwiperSlide>
                <SwiperSlide className='swiper-slide slide-2'>
                <div className='title'>
                        <h1>Stair Case</h1>
                    </div>
                    <div className='content text-center'>
                        <div className='score'>6.6</div>
                        <div className='text'>
                            <h2>Stair</h2>
                            <p>Interior Design Renovation</p>
                        </div>
                    </div>
                </SwiperSlide>
                <SwiperSlide className='swiper-slide slide-3'>
                <div className='title'>
                        <h1>Green House</h1>
                    </div>
                    <div className='content text-center'>
                        <div className='score'>7.2</div>
                        <div className='text'>
                            <h2>Green House</h2>
                            <p>Interior Design Renovation</p>
                        </div>
                    </div>
                </SwiperSlide>
                <SwiperSlide className='swiper-slide slide-4'>
                <div className='title'>
                        <h1>Living Room</h1>
                    </div>
                    <div className='content text-center'>
                        <div className='score'>8.6</div>
                        <div className='text'>
                            <h2>Living Room</h2>
                            <p>Building Renovation</p>
                        </div>
                    </div>
                </SwiperSlide>
                <SwiperSlide className='swiper-slide slide-5'>
                <div className='title'>
                        <h1>Green House</h1>
                    </div>
                    <div className='content text-center'>
                        <div className='score'>8.2</div>
                        <div className='text'>
                            <h2>Green House</h2>
                            <p>Interior Design Renovation</p>
                        </div>
                    </div>
                </SwiperSlide>
                <SwiperSlide className='swiper-slide slide-6'>
                <div className='title'>
                        <h1>Kitchen</h1>
                    </div>
                    <div className='content text-center'>
                        <div className='score'>8.6</div>
                        <div className='text'>
                            <h2>Kitchen</h2>
                            <p>Building Renovation</p>
                        </div>
                    </div>
                </SwiperSlide>
                <SwiperSlide className='swiper-slide slide-7'>
                <div className='title'>
                        <h1>Green House</h1>
                    </div>
                    <div className='content text-center'>
                        <div className='score'>9.0</div>
                        <div className='text'>
                            <h2>Green House</h2>
                            <p>Interior Design Renovation</p>
                        </div>
                    </div>
                </SwiperSlide>
                <div className='swiper-pagination'></div>
                </Swiper>
            </div>
        </main>
  )
}

export default Reviews