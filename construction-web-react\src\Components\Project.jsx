import React from 'react'
import Slider from 'react-slick';

const Project = () => {

    
    let settings = {
        dots: true,
        infinite: true,
        slidesToShow: 3,
        slidesToScroll: 1,
        autoplay: true,
        autoplaySpeed: 2000,
        pauseOnHover: true,
        responsive: [
          {
            breakpoint: 1024,
            settings: {
              slidesToShow: 3,
              slidesToScroll: 3,
              infinite: true,
              dots: true
            }
          },
          {
            breakpoint: 600,
            settings: {
              slidesToShow: 2,
              slidesToScroll: 2,
              initialSlide: 2
            }
          },
          {
            breakpoint: 480,
            settings: {
              slidesToShow: 1,
              slidesToScroll: 1
            }
          }
        ]
      };


      const data=[
        {  
           image: '/building1.jpg', 

        },
        {  
            image: '/building4.jpg', 
 
         },
         {  
            image: '/building5.jpg', 
 
         },
         {  
            image: '/building6.jpg', 
 
         },
         {  
            image: '/project1.jpg', 
 
         },
         {  
            image: '/project2.jpg', 
 
         },
         {  
            image: '/project3.jpg', 
 
         },
    ]

  return (
    <div>
        <div>
        <div className='h-screen -z-10 bg-neutral-50 pt-10 mb-50'>


<div className="flex flex-col md:-ml-90 py-5 justify-center md:gap-8 items-cente md:grid">
    <h2 className="text-5xl font-bold md:block md:text-7xl opacity-14 text-amber-400 flex justify-center">Projects</h2>
    <h3 className='text-3xl p-5 justify-center md:block md:px-2 md:-mt-10 flex  -mt-5 text-neutral-800 font-bold'>Over View of our projects</h3>
    <p className="text-lg p-10 justify-center md:block md:text-l md:text-xl md:px-2 md:-mt-14 w-full max-md:text-center flex -mt-10 px-20 text-neutral-700">Check out beautiful renovations and new construction projects</p>
</div>
<div className='w-3/4 m-auto'>
<Slider {...settings}>
  {
    data.map((item,index)=>{
      return(
        <div key={index} className=' rounded-xl'>
          <div className="">
            <img src={item.image}  />
          </div>
        </div>
      )
    })
  }
</Slider>
</div>
</div>
    </div>
    </div>
  )
}

export default Project;