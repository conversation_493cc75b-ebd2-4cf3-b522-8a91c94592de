import React from 'react'
import { RiCheckFill } from 'react-icons/ri';
import Info2 from './Info2';
import Info3 from './Info3';
import image2 from '../assets/image5.jpg';


const InfoAbout = () => {
  return (
    <div className='md:grid ml-10 mt-10 px-5'>
        <div className='flex flex-col justify-center gap-8 items-center md:grid grid-cols-2 '>

        <div className='mt-10'>
            <img src={image2} alt='CEO' className='rounded-2xl'/>
        </div>
        <div className='flex flex-col z-0 mt-7'>
            <h2 className='text-5xl font-bold md:block md:text-7xl opacity-14 text-amber-400 flex justify-center'>Why Us?</h2>
            <h3 className='text-3xl p-5 justify-center md:block md:px-2 md:-mt-10 flex -mt-5 text-neutral-800 font-bold'>Why <span className='text-amber-400'>&nbsp;Choose Us?</span></h3>
            <p className='text-lg p-10 justify-center md:block md:text-l md:px-2 md:-mt-14 w-full max-md:text-center flex -mt-10 px-20 text-neutral-700'>The Department of Buildings promotes the safety build</p>
            <p className='text-lg p-10 justify-center max-md:text-center md:px-2 md:block flex -mt-12 px-5 text-neutral-700'>From the High-Risk Construction Oversight Study (HRCO), 41 recommendations were developed to strengthen the safety and oversight of these activities recommendations building and the construction.</p>
            <ul className='md:flex gap-5 list-none text-nowrap justify-center flex-col md:text-xs md:-ml-10 grid md:grid-cols-2 mr-14'>
                <li className='flex flex-row md:px-2'>
                    <div><RiCheckFill className='text-amber-400 p-2 hover:bg-amber-400 hover:text-neutral-800  ml-10 bg-amber-50 w-13 h-13 rounded-full' /></div>
                    <div className='font-semibold ml-3 p-2 text-xl md:text-lg md:font-light'>We Are Creative Team</div>
                </li>
                <li className='flex flex-row'>
                    <div><RiCheckFill className='text-amber-400 p-2 hover:bg-amber-400 hover:text-neutral-800  ml-10 bg-amber-50 w-13 h-13 rounded-full' /></div>
                    <div className='font-semibold ml-3 p-2 text-xl md:text-lg md:font-light'>Quality Commitment</div>
                </li>
                <li className='flex flex-row'>
                    <div><RiCheckFill className='text-amber-400 p-2 hover:bg-amber-400 hover:text-neutral-800 ml-10 bg-amber-50 w-13 h-13 rounded-full' /></div>
                    <div className='font-semibold ml-3 p-2 text-xl md:text-lg md:font-light'>Honest And Dependable</div>
                </li>
                <li className='flex flex-row'>
                    <div><RiCheckFill className='text-amber-400 p-2 hover:bg-amber-400 hover:text-neutral-800 ml-10 bg-amber-50 w-13 h-13 rounded-full' /></div>
                    <div className='font-semibold ml-3 p-2 text-xl md:text-lg md:font-light'>We Are Always Improving</div>
                </li>
            </ul>
        </div>
    </div>

    <div className='w-full h-0.5 mt-15 bg-neutral-100'></div>

    <Info2 />
    <Info3 />

</div>
  )
}

export default InfoAbout;