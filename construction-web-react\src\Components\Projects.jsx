import "slick-carousel/slick/slick.css";
import "slick-carousel/slick/slick-theme.css";
import Slider from "react-slick";

const Projects = () => {


    let settings = {
        dots: true,
        infinite: true,
        slidesToShow: 3,
        slidesToScroll: 1,
        autoplay: true,
        autoplaySpeed: 2000,
        pauseOnHover: true,
        responsive: [
          {
            breakpoint: 1024,
            settings: {
              slidesToShow: 3,
              slidesToScroll: 3,
              infinite: true,
              dots: true
            }
          },
          {
            breakpoint: 600,
            settings: {
              slidesToShow: 2,
              slidesToScroll: 2,
              initialSlide: 2
            }
          },
          {
            breakpoint: 480,
            settings: {
              slidesToShow: 1,
              slidesToScroll: 1
            }
          }
        ]
      };


      const data=[
        {  
           text1: '⭐️⭐️⭐️⭐️⭐️',
           text2: '"HorizonEng Builders did a fantastic job with our home renovation project. From the very start, they were transparent, professional, and incredibly reliable. Every step of the way, they kept us informed and stuck to the timeline they promised. The quality of their work exceeded our expectations, and we’re beyond happy with the result. Truly a team you can trust!"',
           text3: ' — <PERSON>, Homeowner',

        },
        {
            text1: '⭐️⭐️⭐️⭐️⭐️',
            text2: '"Finding a construction company you can rely on is rare, but HorizonEng Builders proved to be just that. They delivered excellent craftsmanship, were punctual, and always kept the site clean and organized. I felt confident knowing they handled everything with care and integrity. Highly recommend them for any building or renovation work", Property Developer',
            text3: '.— David K.',
        },
        {
            text1: '⭐️⭐️⭐️⭐️⭐️',
            text2: '"Working with HorizonEng Builders was one of the best decisions we made for our project. They are not just skilled professionals but also trustworthy individuals who genuinely care about their clients. The attention to detail in their work shows their commitment to quality. I wouldnt hesitate to use their services again."',
            text3: '— Linda R., Commercial Property Owner',
        },
        {
            text1:'⭐️⭐️⭐️⭐️⭐️',
            text2: '"I couldn’t be happier with the work HorizonEng Builders did on our property. They were professional, dependable, and always kept us in the loop. Their workmanship was top-notch, and they finished ahead of schedule without cutting corners. A trustworthy team that truly delivers quality!""',
            text3: '— Samuel O., Residential Client',
        },
        {
            text1:'⭐️⭐️⭐️⭐️⭐️',
            text2: '"HorizonEng Builders turned our vision into reality with precision and dedication. What impressed me most was how dependable and responsive they were throughout the entire process. No shortcuts, no surprises—just honest, high-quality work. They’ve earned my trust completely."',
            text3: '— Mark T., Home Renovation Client',

        },
        {
            text1:'⭐️⭐️⭐️⭐️⭐️',
            text2: '"HorizonEng Builders are true professionals. They handled our building project with great skill and consistency from start to finish. What stood out most was their honesty and how easy they were to communicate with. They delivered exactly what they promised—on time and within budget. It’s rare to find such a reliable team these days!"',
            text3: '— Jennifer A., Satisfied Client',
        }
      ]



  return (
    <div>
        <div className='h-screen bg-neutral-50 pt-10 mb-8 md:mb-60'>


<div className="flex flex-col md:-ml-90 py-5 justify-center md:gap-8 items-cente md:grid">
    <h2 className="text-5xl font-bold md:block md:text-7xl opacity-14 text-amber-400 flex justify-center">CLIENTS</h2>
    <h3 className='text-3xl p-5 justify-center md:block md:px-2 md:-mt-10 flex  -mt-5 text-neutral-800 font-bold'>Testimonials</h3>
    <p className="text-lg p-10 justify-center md:block md:text-l md:text-xl md:px-2 md:-mt-14 w-full max-md:text-center flex -mt-10 px-20 text-neutral-700">Check out why our customers loves us in each projects</p>
</div>
<div className='w-4/5 m-auto'>
<Slider {...settings}>
  {
    data.map((item,index)=>{
      return(
        <div key={index} className='p-5 rounded-xl'>
          <div className="p- text-lg h-2">
            <p>{item.text1}</p>
            <br></br>
            <p>{item.text2}</p>
            <br></br>
            <p>{item.text3}</p>
          </div>
        </div>
      )
    })
  }
</Slider>
</div>
</div>
    </div>
  )
}

export default Projects;