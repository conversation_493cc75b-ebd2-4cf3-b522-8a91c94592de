import React, { useEffect, useRef, useState } from 'react';
import { RiArrowDropDownLine, RiChat1Fill, RiChatSmileAiFill, RiChatSmileAiLine, RiCloseLargeLine, RiCloseLine } from "react-icons/ri";
import ChatFor from './ChatFor';
import ChatMessage from './ChatMessage';
import { companyInfo } from '../companyInfo';


const ChatBot = () => {

    const [chatHistory, setChatHistory] =  useState([{
        hideInChat: true,
        role: "model",
        text: companyInfo
    }]);
    const [showChatBot, setShowChatBot] =  useState(false);

    const chatBodyRef = useRef();

    const generateBotResponse  = async (history) => {

        const updateHistory = (text, isError = false) => {
            setChatHistory(prev => [...prev.filter(msg => msg.text !== "Thinking..."), {role: "model", text, isError}]);
        } 

        
        history = history.map(({role, text}) => ({role, parts: [{text}]}));

        const requestOptions = {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ contents: history })
        };

        try {
            const response =  await fetch(import.meta.env.VITE_API_URL, requestOptions);
            const data = await response.json();
            if (!response.ok) throw new Error(data.error.message || "Something went wrong");
            
            const apiRequestText = data.candidates[0].content.parts[0].text.replace(/\*\*(.*?)\*\*/g, "$1").trim();
            updateHistory(apiRequestText);
            
        } catch (error) {
            updateHistory(error.message, true);
        }
    }


    useEffect(() => {
        chatBodyRef.current.scrollTo({top: chatBodyRef.current.scrollHeight, behavior: "smooth"});
    }, [chatHistory])

  return (

    <div className={`container relative ${showChatBot ? 'show-chatbot' : ''}`}>
        <button onClick={() => setShowChatBot(prev => !prev)} className=' flex fixed sm:bottom-10 max-md:bottom-40 max-md:right-5 sm:right-5  bg-amber-400 w-15 h-15 rounded-full flex-col ' id='chatbot-toggle'>
            <span><RiChat1Fill className='text-4xl' /></span>
            <span><RiCloseLine className='text-4xl' /></span>
        </button>

        <div className='chatbot-popup max-md:bottom-7 max-sm:right-18 max-sm:w-85 max-sm:bottom-4 max-sm:80 md:w-100 bottom-10 right-18 fixed'>
            <div className='chatbot-header bg-amber-400'>
                <div className='header-info'>
                    <RiChatSmileAiFill className='h-5 w-5 text-amber-400 bg-white rounded-full' />
                    <h2 className='logo-text'>Chatbot</h2>
                </div>
                <button onClick={() => setShowChatBot(prev => !prev)} className='material-symbols-rounded'>
                    <RiArrowDropDownLine className='h-5 w-5 shadow-md font-bold pt-0.5 -mr-1 rounded-full bg-amber-400 cursor-pointer hover:bg-amber-500 transition-all ease-in-out text-7xl' />
                </button>
            </div>
            
                <div ref={chatBodyRef} className='chatbot-body'>
                    <div className='message chatbot-message'>
                        <RiChatSmileAiLine className='h-5 w-5 self-end text-white rounded-full' />
                        <p className='message-text bg-amber-100'>Hey there 👋 How can I help you today?</p>
                    </div>

                    
                    {chatHistory.map((chat, index) => (
                        <ChatMessage key={index} chat={chat}/>
                    ))}
                </div>
        
                <div className='chatbot-footer absolute bottom-6 -left-5 -mb-7 bg-white pb-3 pt-5 w-full'>
                    <ChatFor chatHistory={chatHistory} setChatHistory={setChatHistory} generateBotResponse={generateBotResponse} />
                </div>
                </div>
        </div>
    
  )
}

export default ChatBot


