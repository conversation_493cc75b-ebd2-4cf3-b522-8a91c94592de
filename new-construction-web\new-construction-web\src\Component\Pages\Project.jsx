import "slick-carousel/slick/slick.css";
import "slick-carousel/slick/slick-theme.css";
import Slider from "react-slick";
import NavBar from '../NavBar'
import Footer from '../Footer'
import ChatBot from "../ChatBot/ChatBot";

function Project() {



  let settings = {
    dots: true,
    infinite: true,
    slidesToShow: 3,
    slidesToScroll: 1,
    autoplay: true,
    autoplaySpeed: 2000,
    pauseOnHover: true,
    responsive: [
      {
        breakpoint: 1024,
        settings: {
          slidesToShow: 3,
          slidesToScroll: 3,
          infinite: true,
          dots: true
        }
      },
      {
        breakpoint: 600,
        settings: {
          slidesToShow: 2,
          slidesToScroll: 2,
          initialSlide: 2
        }
      },
      {
        breakpoint: 480,
        settings: {
          slidesToShow: 1,
          slidesToScroll: 1
        }
      }
    ]
  };


  const data=[
    {  
       image: '/building1.jpg', 

    },
    {  
        image: '/building4.jpg', 

     },
     {  
        image: '/building1.jpg', 

     },
     {  
        image: '/building6.jpg', 

     },
     {  
        image: '/project1.jpg', 

     },
     {  
        image: '/project2.jpg', 

     },
     {  
        image: '/project3.jpg', 

     },
]

return (
<div>
  <NavBar />
    <div className='about-container'>
    <div className='about-wrapper'>
    <div className="about-content">
    <h2 className="text-5xl font-bold md:block md:text-7xl opacity-14 text-amber-400 flex justify-center">Projects</h2>
    <h3 className='text-3xl p-5 justify-center md:block md:px-2 md:-mt-10 flex  -mt-5 text-neutral-800 font-bold'>Over View of our projects</h3>
    <p className="text-lg p-10 justify-center md:block md:text-l md:text-xl md:px-2 md:-mt-14 w-full max-md:text-center flex -mt-10 px-20 text-neutral-700">Check out beautiful renovations and new construction projects</p>
</div>
<div className='w-3/4 ml-10'>
<Slider {...settings}>
{
data.map((item,index)=>{
  return(
    <div key={index} className=' rounded-xl m-auto'>
      <div className="">
        <img src={item.image}  />
      </div>
    </div>
  )
})
}
</Slider>
</div>
</div>
</div>
<ChatBot />
<Footer />
</div>

)
}
export default Project;