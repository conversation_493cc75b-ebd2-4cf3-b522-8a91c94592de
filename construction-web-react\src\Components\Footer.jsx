import React from 'react'
import { Link, NavLink } from 'react-router-dom'
import ButtonMailto from './ButtonMailto'

export const Footer = () => {
    const currentYear = new Date().getFullYear();
  return (
    <div className='footer mt-10 text-neutral-200 max-md:text-center w-fit'>
        <footer className='grid grid-cols-4 gap-4 bg-neutral-800 max-md:flex max-md:flex-col'>
            <div className='py-10 px-6'>
            <h2 className='text-amber-400 text-xl mb-3'>Department of Construction</h2>
            <p>The Department of Construction promotes the safety of all people that build, work, and live in New York City by regulating the lawful use of over one million buildings.</p>
            </div>

            <div className='list-none p-10'>
            <h2 className='text-amber-400 text-xl mb-3'>Short cuts</h2>
            <ul className='flex flex-col'>
                <NavLink className='hover:bg-amber-400 hover:border-b-amber-400 hover:text-amber-400' to={"/"}>Home</NavLink>
                <NavLink className='' to={"/about"}>The Company</NavLink>
                <NavLink className='' to={"/services"}>Services</NavLink>
                <NavLink className='' to={"/fag"}>FAQ</NavLink>
                <NavLink className='' to={"/project"}>Project</NavLink>
                <NavLink className='' to={"/contact"}>Contact</NavLink>
            </ul>
            </div>

            <div className='list-none p-10'>
            <h2 className='text-amber-400 text-xl mb-3'>Construction Office</h2>
            <li>17 State Street 40th floor Financial District New York, NY 10004</li>
            <li className='hover:text-amber-400'><ButtonMailto label={"<EMAIL>"} mailto="mailto:<EMAIL>" /></li>
            </div>

            <div className='list-none px-5 py-10'>
            <h2 className='text-amber-400 text-xl mb-3'>Working hours</h2>
            <ul className='max-md:ml-20 '>
            <li className='flex flex-row'>Monday to Friday &nbsp; <div className='bg-neutral-700 text-neutral-700 h-0.5 w-10 mt-3'></div>&nbsp; 8AM - 4PM</li>
            <li className='flex flex-row'>Saturday &nbsp; <div className='bg-neutral-700 text-neutral-700 h-0.5 w-10 mt-3'></div>&nbsp; 8AM - 1PM </li>
            <li className='flex flex-row'>Sunday &nbsp; <div className='bg-neutral-700 text-neutral-700 h-0.5 w-10 mt-3'></div>&nbsp; Support by Email </li>
            </ul>
            </div>
        </footer>
        <div className='bg-white text-neutral-400 p-10 h-25'>
            <p>&copy; {currentYear} Copyright Horizonengbuilders. All right reserved</p>
        </div>
    </div>
  )
}
