import image3 from '../assets/image4.jpg';

const Info2 = () => {
  return (
    <div>
        
    <div className='flex flex-col justify-center md:gap-8 items-center md:grid grid-cols-2'>
    <div className='flex flex-col z-0 mt-7 px-0'>
        <h2 className='text-5xl font-bold md:block md:text-7xl opacity-14 text-amber-400 flex justify-center'>Our Team</h2>
        <h3 className='text-3xl p-5 justify-center md:block md:px-2 md:-mt-10 flex -mt-5 text-neutral-800 font-bold'>Who <span className='text-amber-400'>&nbsp;We Are?</span></h3>
        <p className='text-lg p-10 justify-center md:block md:text-l md:px-2 md:-mt-14 w-full max-md:text-center flex -mt-10 px-20 text-neutral-700'>Meet our team members and our work places</p>
        <p className='text-lg p-10 justify-center max-md:text-center md:px-2 md:block flex -mt-12 px-5 text-neutral-700'>From the High-Risk Construction Oversight Study (HRCO), 41 recommendations were developed to strengthen the safety and oversight of these activities recommend.
        Construction is a dangerous business and workers must take every precaution to protect themselves from the risks of the job. Employers are required by law to provide construction workers with safety harnesses.</p>
        <ul className='md:flex gap-5 flex-row list-none justify-center max-md:text-wrap md:-ml-10 grid md:grid-cols-2'>
            <li className='flex md:px-0 text-center flex-col items-center md:items-center'>
                <p className=' text-4xl mb-5'>50</p>
                <p className='font-semibold max-md:w-60 text-center max-md:mr-20 max-md:ml-20 px-6 py-2 text-xl text-neutral-800 bg-amber-400 hover:bg-neutral-800 rounded-full hover:text-amber-400'>Years Of Experience</p>
            </li>
            <li className='flex md:px-2 text-center flex-col md:items-center'>
                <div className='text-4xl mb-5'>90+</div>
                <p className='font-semibold max-md:w-60 text-center max-md:ml-20 px-6 py-2 text-xl text-neutral-800 bg-amber-400 hover:bg-neutral-800 rounded-full hover:text-amber-400'>Projects Done</p>
            </li>
            <li className='flex md:px-2 text-center flex-col md:items-center'>
                <div className='text-4xl mb-5'>70k</div>
                <p className='font-semibold max-md:w-60 text-center max-md:ml-20 px-6 py-2 text-xl text-neutral-800 bg-amber-400 hover:bg-neutral-800 rounded-full hover:text-amber-400'>Award Winning</p>
            </li>
        </ul>
    </div>
    <div className='md:ml-15 mt-10 '>
        <img src={image3} alt='CEO' className='rounded-2xl'/>
    </div>
    </div>
    </div>
  )
}

export default Info2