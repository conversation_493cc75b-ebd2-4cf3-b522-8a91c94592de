import React from 'react'
import { RiAccountBoxFill, Ri<PERSON><PERSON>WordFill, Ri<PERSON>ome4Fill, RiQuestionAnswerFill } from 'react-icons/ri'
import { NavLink } from 'react-router-dom'

const BottomNavBar = () => {
  return (
    <>
    <div className="header sticky z-10 bottom-10 mx-5 md:hidden max-md:full max-md:flex rounded-2xl  h-20 ml-2 bg-amber-400 drop-shadow-md text-neutral-700 gap-8 p-2 justify-center">
        <div className='px-2'>
            <ul className='justify-center items-center gap-0 font-jost flex'>
                    <NavLink className='w-20 h-15 mb-4 flex flex-col justify-center items-center align-middle rounded-2xl' to={"/"}><RiHome4Fill className='text-2xl text-white' />Home</NavLink>
                    <NavLink className='w-20 h-15 mb-4 flex flex-col justify-center items-center align-middle rounded-2xl' to={"/project"}><RiFileWordFill className='text-2xl text-white' />Projects</NavLink>
                    <NavLink className='w-20 h-15 mb-4 flex flex-col justify-center items-center align-middle rounded-2xl' to={"/services"}><RiAccountBoxFill className='text-2xl text-white' />Services</NavLink>
                    <NavLink className='w-20 h-15 mb-4 flex flex-col justify-center items-center align-middle rounded-2xl' to={"/contact"}><RiQuestionAnswerFill className='text-2xl text-white' />Contact</NavLink>
            </ul>
        </div>
    </div>
    </>
    
  )
}

export default BottomNavBar